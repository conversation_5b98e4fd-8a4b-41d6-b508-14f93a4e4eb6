[project]
name = "upzi-career-path"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12,<3.14"
dependencies = [
    "fastapi>=0.115.11",
    "langchain>=0.3.20",
    "langchain-community>=0.3.19",
    "langchain-openai>=0.3.8",
    "langgraph>=0.3.5",
    "neo4j>=5.28.1",
    "pydantic-settings>=2.8.1",
    "python-dotenv>=1.0.1",
    "sentry-sdk>=2.22.0",
    "trustcall>=0.0.38",
    "uvicorn>=0.34.0",
    "redis>=5.2.1",
    "langfuse>=2.60.1",
]

[dependency-groups]
dev = [
    "ipykernel>=6.29.5",
    "pre-commit>=4.1.0",
    "ruff>=0.9.9",
    "streamlit>=1.43.2",
    "pytest>=8.1.1",
    "pytest-asyncio>=0.23.5",
]

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
