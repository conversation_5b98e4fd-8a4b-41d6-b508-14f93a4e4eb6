import os
from contextlib import asynccontextmanager
from typing import Any, Dict, List, Optional

from dotenv import load_dotenv
from neo4j import AsyncGraphDatabase

load_dotenv()


class Neo4jService:
    def __init__(self, uri: str, user: str, password: str):
        self._uri = uri
        self._username = user
        self._password = password
        self._driver = None

    async def connect(self) -> None:
        """Establish connection to Neo4j database."""
        if not self._driver:
            try:
                self._driver = AsyncGraphDatabase.driver(
                    self._uri, auth=(self._username, self._password)
                )
                async with self._driver.session() as session:
                    await session.run("RETURN 1")
            except Exception as e:
                raise ConnectionError(f"Failed to connect to Neo4j: {str(e)}")

    async def close(self) -> None:
        if self._driver:
            await self._driver.close()
            self._driver = None

    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """Async context manager exit."""
        await self.close()

    async def execute_query(
        self, query: str, parameters: Optional[Dict[str, Any]] = None
    ) -> List[Any]:
        """Run a Cypher query asynchronously.

        Args:
            query: Cypher query string
            parameters: Optional query parameters

        Returns:
            Query results as a list
        """
        if parameters is None:
            parameters = {}

        try:
            async with self._driver.session() as session:
                result = await session.run(query, parameters)
                records = await result.data()
                await result.consume()
                return records
        except Exception as e:
            raise Exception(f"Query execution failed: {str(e)}")


@asynccontextmanager
async def get_neo4j_service():
    service = Neo4jService(
        uri=os.getenv("NEO4J_URI", ""),
        user=os.getenv("NEO4J_USER", ""),
        password=os.getenv("NEO4J_PASSWORD", ""),
    )
    await service.connect()
    try:
        yield service
    finally:
        await service.close()
