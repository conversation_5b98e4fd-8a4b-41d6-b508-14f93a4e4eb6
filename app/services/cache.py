import json
import logging
from redis.exceptions import RedisError

from app.services.redis import CACHE_TTL, redis_service

logger = logging.getLogger(__name__)


async def cache_get(key: str) -> dict | None:
    """Get data from Redis cache with error handling"""
    try:
        data = await redis_service.get(key)
        if data:
            return json.loads(data)
        return None
    except RedisError as e:
        logger.error(f"Redis error when getting cache: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error when getting cache: {str(e)}")
        return None


async def cache_set(key: str, value: dict, expire: int = CACHE_TTL) -> bool:
    """Set data to Redis cache with error handling"""
    try:
        await redis_service.set(key, json.dumps(value), ex=expire)
        return True
    except RedisError as e:
        logger.error(f"Redis error when setting cache: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error when setting cache: {str(e)}")
        return False


def generate_cache_key(prefix: str, data: dict) -> str:
    """Generate a cache key based on input data"""
    sorted_data = {k: data[k] for k in sorted(data.keys()) if data[k] is not None}
    data_str = json.dumps(sorted_data, sort_keys=True)
    return f"{prefix}:{hash(data_str)}"
