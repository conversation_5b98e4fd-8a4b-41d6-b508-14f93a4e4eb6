from typing import List

from typing_extensions import TypedDict


class InputState(TypedDict):
    career_path: str
    major: str
    university: str
    skills_gained: str
    experience: str
    language_level: str
    hobbies: str
    characteristics: str
    favourite_subject: str


class AgentState(InputState):
    group_function: str
    evaluates: List[dict]
    career_paths: List[dict]
    responses: List[dict]


class OutputState(TypedDict):
    responses: List[dict]
