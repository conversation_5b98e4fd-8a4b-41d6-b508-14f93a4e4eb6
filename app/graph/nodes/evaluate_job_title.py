import math
from enum import Enum
from typing import List

from dotenv import load_dotenv
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_openai import Chat<PERSON><PERSON>A<PERSON>
from pydantic import BaseModel, Field

from app.graph.state import AgentState

load_dotenv()


class Criterion(Enum):
    MAJOR = "Major"
    UNIVERSITY = "University"
    FAVOURITE_SUBJECT = "Favourite subject"
    SKILLS_GAINED = "Skills gained"
    EXPERIENCE = "Experience"
    HOBBIES = "Hobbies"
    CHARACTERISTICS = "Characteristics"
    LANGUAGE_LEVEL = "Language level"


class CriterionWeights(Enum):
    MAJOR = 30
    SKILLS_GAINED = 20
    EXPERIENCE = 10
    UNIVERSITY = 10
    LANGUAGE_LEVEL = 10
    HOBBIES = 7
    CHARACTERISTICS = 8
    FAVOURITE_SUBJECT = 5


class Evaluation(BaseModel):
    criterion: Criterion
    score: int


job_level_mapping = {
    "Student": "student",
    "Entry": "entry",
    "Experienced": "experienced",
    "Senior": "senior",
    "Manager": "manager",
    "Expert": "expert",
    "Director or above": "director_or_above",
}

JobLevel = Enum("JobLevel", job_level_mapping)


class EvaluationResult(BaseModel):
    evaluation: List[Evaluation]
    reasoning: List[str]
    current_level: JobLevel = Field(
        description="The current level of the user profile in the job title"
    )


EVALUATE_JOB_TITLE = """You are an AI assistant specialized in career matching. Your task is to evaluate how well a user's profile matches a specific career path based on given criteria. Please analyze the following information carefully:

<user_profile>
{user_profile}
</user_profile>

The career path to evaluate against is:
<career_path>
{career_path}
</career_path>

Before providing your evaluation, please think through the process carefully. Use the following <analysis> tags inside your thinking block to show your analysis:

<analysis>
1. Extract key information from the user's profile:
   - Education background
   - Personality traits
   - Interests
   - Skills
   - Favorite subjects
   - Work experience
   - Language proficiency

2. List out the requirements and characteristics of the specified career path.

3. Compare the user's profile with the career path requirements:
   For each of the following criteria, note how well the user's attributes match the career requirements:
   a. Ngành học phù hợp với công việc
   b. Trường đại học danh tiếng về ngành học
   c. Tính cách
   d. Sở thích
   e. Kỹ năng phù hợp với công việc
   f. Môn học yêu thích phù hợp với công việc
   g. Kinh nghiệm làm việc phù hợp với công việc
   h. Trình độ ngoại ngữ

4. Determine the current level based on the user's experience.
</analysis>

After your analysis, provide your evaluation using the following format:

<evaluation>
Criterion: [criterion_name]
Score: [0-10, or -1 if information is missing]
</evaluation>

<reasoning>
[Provide reasoning in Vietnamese using bullet points. Include only criteria where the user has provided information. Maximum 3 bullet points, each max 100 characters. Bullet point 1: 'Ngành học, Trường đại học, môn học yêu thích', Bullet point 2: 'Sở thích hoặc tính cách', Bullet point 3: 'Kỹ năng'. Skip any bullet point where all information is missing.]
</reasoning>

<current_level>
Current level: [Evaluate the user's current job level based on their experience. If user has no experience, return "student"]
</current_level>

Please provide your evaluation for all criteria, following the format specified above. Your final output should consist only of the evaluation, reasoning, and current level, and should not duplicate or rehash any of the work you did in the analysis section."""


def process_evaluation(evaluation):
    total_score = 0
    total_weight = 0
    for data in evaluation["evaluation"]:
        criterion = data["criterion"]
        score = data["score"]
        weight = CriterionWeights[Criterion(criterion).name].value
        if score != -1:
            total_weight += weight
            total_score += score * weight / 10
        else:
            total_score += 0
    if total_weight == 0:
        return 0
    overall_score = int(math.ceil(total_score * 100 / total_weight))
    return overall_score


async def evaluate_job_title(state: AgentState):
    user_profile = f"""Ngành học: {state["major"] if state["major"] else "Không có thông tin"}
Trường đại học: {state["university"] if state["university"] else "Không có thông tin"}
Tính cách: {state["characteristics"] if state["characteristics"] else "Không có thông tin"}
Sở thích: {state["hobbies"] if state["hobbies"] else "Không có thông tin"}
Kỹ năng : {state["skills_gained"] if state["skills_gained"] else "Không có thông tin"}
Môn học yêu thích: {state["favourite_subject"] if state["favourite_subject"] else "Không có thông tin"}
Kinh nghiệm làm việc: {state["experience"] if state["experience"] else "Không có thông tin"}
Trình độ ngoại ngữ: {state["language_level"] if state["language_level"] else "Không có thông tin"}"""

    llm = ChatOpenAI(model="gpt-4.1-mini")
    evaluate_prompt = ChatPromptTemplate.from_template(template=EVALUATE_JOB_TITLE)
    evaluation_chain = evaluate_prompt | llm.with_structured_output(EvaluationResult)

    evaluates = {}

    if isinstance(state.get("career_paths"), list):
        input = [
            {"user_profile": user_profile, "career_path": career_path}
            for career_path in state["career_paths"]
        ]
        results = await evaluation_chain.abatch(input)
        for result, career_path in zip(results, state["career_paths"]):
            evaluation = result.model_dump(mode="json")
            matching_score = process_evaluation(evaluation)
            evaluates[career_path] = {
                "current_level": evaluation["current_level"],
                "matching_score": matching_score,
                "why_you_fit": "\n".join(evaluation["reasoning"]),
            }
    else:
        evaluation_response = await evaluation_chain.ainvoke(
            {"user_profile": user_profile, "career_path": state["career_path"]}
        )
        evaluation = evaluation_response.model_dump(mode="json")
        matching_score = process_evaluation(evaluation)
        evaluates[state["career_path"]] = {
            "current_level": evaluation["current_level"],
            "matching_score": matching_score,
            "why_you_fit": "\n".join(evaluation["reasoning"]),
        }
    return {"evaluates": evaluates}
