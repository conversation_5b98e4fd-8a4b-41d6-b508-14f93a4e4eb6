from app.graph.state import AgentState
from app.services.neo4j import get_neo4j_service


async def get_career_path(state: AgentState):
    group_function = state.get("group_function", "")
    if state.get("career_paths"):
        career_paths = state["career_paths"]
    else:
        career_paths = [state["career_path"]]

    list_career_path = []
    async with get_neo4j_service() as neo4j:
        for career_path in career_paths:
            if group_function == "":
                group_function_query = "MATCH (jt1:JobTitleV2)-[r:ADVANCES_TO {career_path: $career_path}]->(jt2:JobTitleV2) RETURN jt1.group_function as group_function"
                group_function_records = await neo4j.execute_query(
                    group_function_query,
                    {"career_path": career_path},
                )
                if len(group_function_records) > 0:
                    group_function = group_function_records[0]["group_function"]
                else:
                    raise ValueError(f"No group function found - {career_path}")
            data = {}
            # Base query for job progression
            path_query = """MATCH (jt1:JobTitleV2)-[r:ADVANCES_TO {career_path: $career_path}]->(jt2:JobTitleV2)
WHERE jt1.group_function = $group_function
WITH jt1, jt2, r,
     CASE
       WHEN jt1.level IN ['entry', 'experienced', 'senior'] THEN 'standard'
       WHEN jt1.level = 'expert' AND "expert" IN r.path_types THEN 'expert'
       WHEN jt1.level = 'manager' AND "manager" IN r.path_types THEN 'manager'
       ELSE null
     END AS path_category
WHERE path_category IS NOT NULL
WITH path_category,
     COLLECT(DISTINCT {job_level: jt1.level, job_title: jt1.name_en, salary_min: jt1.salary_min, salary_max: jt1.salary_max, market_outlook: jt1.market_outlook}) +
     COLLECT(DISTINCT {job_level: jt2.level, job_title: jt2.name_en, salary_min: jt2.salary_min, salary_max: jt2.salary_max, market_outlook: jt2.market_outlook}) AS all_jobs
UNWIND all_jobs AS job
WITH path_category, job,
     CASE
       WHEN path_category = 'standard' THEN
         CASE job.job_level
           WHEN 'entry' THEN 1
           WHEN 'experienced' THEN 2
           WHEN 'senior' THEN 3
           ELSE 4
         END
       WHEN path_category = 'expert' THEN
         CASE job.job_level
           WHEN 'expert' THEN 1
           WHEN 'director_or_above' THEN 2
           ELSE 3
         END
       WHEN path_category = 'manager' THEN
         CASE job.job_level
           WHEN 'manager' THEN 1
           WHEN 'director_or_above' THEN 2
           ELSE 3
         END
     END AS level_order
WHERE
  (path_category = 'standard' AND level_order < 4) OR
  (path_category IN ['expert', 'manager'] AND level_order < 3)
WITH path_category, job, level_order
ORDER BY path_category, level_order
WITH path_category, COLLECT(DISTINCT job) AS job_progression
RETURN
  COALESCE(HEAD([jp IN COLLECT({category: path_category, jobs: job_progression}) WHERE jp.category = 'standard']).jobs, []) AS standard_progression,
  COALESCE(HEAD([jp IN COLLECT({category: path_category, jobs: job_progression}) WHERE jp.category = 'expert']).jobs, []) AS expert_progression,
  COALESCE(HEAD([jp IN COLLECT({category: path_category, jobs: job_progression}) WHERE jp.category = 'manager']).jobs, []) AS manager_progression"""

            path_records = await neo4j.execute_query(
                path_query,
                {"career_path": career_path, "group_function": group_function},
            )
            if path_records:
                standard_progression = path_records[0]["standard_progression"]
                expert_progression = path_records[0]["expert_progression"]
                manager_progression = path_records[0]["manager_progression"]
                data["job_progression"] = {
                    "standard": standard_progression,
                    "expert": expert_progression,
                    "manager": manager_progression,
                }
            else:
                raise ValueError(f"No job progression found - {career_path}")

            # Base query for career path info
            career_path_query = """MATCH (j1:JobTitleV2)-[r:ADVANCES_TO {career_path: $career_path}]->(j2:JobTitleV2)
WHERE j1.level = "experienced"
AND j1.group_function = $group_function
RETURN r.career_path_id as career_path_id, j1.description as description, j1.function as job_function"""

            career_path_records = await neo4j.execute_query(
                career_path_query,
                {
                    "career_path": career_path,
                    "group_function": group_function,
                },
            )
            if len(career_path_records) > 0:
                data["career_path"] = career_path
                data["group_function"] = group_function
                career_path_id = career_path_records[0]["career_path_id"]
                data["career_path_id"] = career_path_id
                description = career_path_records[0]["description"].split(". ")
                data["description"] = description
                job_function = career_path_records[0]["job_function"]
                data["job_function"] = job_function
            else:
                raise ValueError(f"No career path found - {career_path}")

            # Base query for skills
            skills_query = """MATCH (j1:JobTitleV2)-[r:ADVANCES_TO {career_path: $career_path}]->(j2:JobTitleV2)
WHERE j1.level = "experienced" AND j2.group_function = $group_function
WITH j2
MATCH (j2)-[r:UTILIZES]->(s:SkillV2)
WHERE s.type = "soft"
WITH j2, COLLECT({name: s.name_en, description: s.description}) AS soft_skills
MATCH (j2)-[r:UTILIZES]->(s:SkillV2)
WHERE s.type = "hard"
WITH soft_skills, COLLECT({name: s.name_en, description: s.description}) AS hard_skills
RETURN soft_skills, hard_skills"""

            skills_records = await neo4j.execute_query(
                skills_query,
                {
                    "career_path": career_path,
                    "group_function": group_function,
                },
            )
            if len(skills_records) > 0:
                hard_skills = skills_records[0]["hard_skills"][:4]
                soft_skills = skills_records[0]["soft_skills"][:4]
                data["skills_needed"] = {
                    "technical_skills": hard_skills,
                    "soft_skills": soft_skills,
                }
            else:
                raise ValueError(f"No skills found - {career_path}")
            list_career_path.append(data)

        return {"career_paths": list_career_path}
