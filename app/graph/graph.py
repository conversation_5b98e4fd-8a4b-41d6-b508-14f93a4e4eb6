from langgraph.graph import END, START, StateGraph

from app.graph.nodes import (
    evaluate_job_title,
    get_career_path,
    postprocess_career_path,
    suggest_job_title,
)
from app.graph.state import AgentState, InputState, OutputState

workflow = StateGraph(AgentState, input=InputState, output=OutputState)


def suggest_or_generate(state: AgentState):
    if state.get("career_path"):
        return "evaluate_job_title"
    return "suggest_job_title"


workflow.add_node("suggest_job_title", suggest_job_title)
workflow.add_node("evaluate_job_title", evaluate_job_title)
workflow.add_node("get_career_path", get_career_path)
workflow.add_node("postprocess_career_path", postprocess_career_path)

workflow.add_conditional_edges(
    START, suggest_or_generate, ["suggest_job_title", "evaluate_job_title"]
)
workflow.add_edge("suggest_job_title", "evaluate_job_title")
workflow.add_edge("evaluate_job_title", "get_career_path")
workflow.add_edge("get_career_path", "postprocess_career_path")
workflow.add_edge("postprocess_career_path", END)

graph = workflow.compile()
