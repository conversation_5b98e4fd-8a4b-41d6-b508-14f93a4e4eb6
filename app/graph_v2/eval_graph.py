from langgraph.graph import END, START, StateGraph

from app.graph_v2.nodes.evaluate_job_title import evaluate_job_title
from app.graph_v2.nodes.postprocess_evaluation import postprocess_evaluation
from app.graph_v2.state import EvalInputState, EvalOutputState, EvalState

workflow = StateGraph(EvalState, input=EvalInputState, output=EvalOutputState)

workflow.add_node("evaluate_job_title", evaluate_job_title)
workflow.add_node("postprocess_evaluation", postprocess_evaluation)
workflow.add_edge(START, "evaluate_job_title")
workflow.add_edge("evaluate_job_title", "postprocess_evaluation")
workflow.add_edge("postprocess_evaluation", END)

graph = workflow.compile()
