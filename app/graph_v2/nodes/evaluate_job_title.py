import math
from enum import Enum
from typing import List

from dotenv import load_dotenv
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_openai import Chat<PERSON><PERSON>A<PERSON>
from pydantic import BaseModel, Field

from app.graph_v2.state import EvalState
from app.services.cache import cache_get, cache_set, generate_cache_key
from app.services.neo4j import get_neo4j_service

load_dotenv()


class Criterion(Enum):
    MAJOR = "Major"
    UNIVERSITY = "University"
    FAVOURITE_SUBJECT = "Favourite subject"
    SKILLS_GAINED = "Skills gained"
    EXPERIENCE = "Experience"
    HOBBIES = "Hobbies"
    CHARACTERISTICS = "Characteristics"
    LANGUAGE_LEVEL = "Language level"


class CriterionWeights(Enum):
    MAJOR = 30
    EXPERIENCE = 20
    SKILLS_GAINED = 10
    UNIVERSITY = 10
    LANGUAGE_LEVEL = 10
    HOBBIES = 7
    CHARACTERISTICS = 8
    FAVOURITE_SUBJECT = 5


class Evaluation(BaseModel):
    criterion: Criterion
    score: int


job_level_mapping = {
    "Student": "student",
    "Entry": "entry",
    "Experienced": "experienced",
    "Senior": "senior",
    "Expert": "expert",
    "Manager": "manager",
    "Director or above": "director_or_above",
}

JobLevel = Enum("JobLevel", job_level_mapping)


class EvaluationResult(BaseModel):
    evaluation: List[Evaluation]
    current_level: JobLevel = Field(
        description="The current level of the user profile in the job title"
    )


HIGH_SCORE_PROMPT = """# System Prompt for Job Matching Response Generator

You are a career advisor assistant that creates personalized job matching assessments for candidates. When provided with a user's profile and a job position, you will generate a concise evaluation of their fit for the role.

## Response Format Requirements:
- Response will have two parts: generic info and detailed info
- Maximum 200 words total
- Write in Vietnamese
- Return the response in a specific JSON format as shown in the example

## Generic Info Requirements:
- Start with a title "Bạn có nền tảng tốt cho vị trí [career_path]"
- Follow with an encouraging summary statement as a paragraph (no bullet points)

## Detailed Info Requirements:
- Begin with the matching percentage between the user and career path
- Structure detailed info in clear sections with titles (education, experience, personality, interests)
- Each section should have 2 bullet points with relevant information
- Directly reference all available user-provided information from this list:
  • Major/degree
  • University name
  • Skills
  • Work experience
  • Language proficiency
  • Hobbies/interests
  • Personal characteristics
  • Favorite subjects
- For each element, explicitly connect it to specific job requirements
- Highlight transferable skills relevant to the position
- Mention 2 potential growth areas while maintaining an overall positive tone

## Handling Missing Information:
- If any information is missing, do not explicitly mention the absence
- Adjust the matching percentage score downward by approximately 5-10% for each missing major category
- Focus more heavily on the information that is available
- Maintain the same category structure, but combine categories if needed due to limited information
- For missing education details, emphasize skills and experience more strongly
- For missing experience, focus on educational background and transferable skills from hobbies/interests
- If personality traits are missing, infer potential strengths from other provided information
- Always maintain at least 3 distinct category sections regardless of missing information

## Tone and Style:
- Professional but warm
- Encouraging and constructive
- Use emoji sparingly (1-2 maximum)
- Focus on strengths while acknowledging areas for development

## Example response format:

```json
{
  "generic_info": {
    "blocks": {
      "title": "Bạn có nền tảng tốt cho vị trí Product Manager",
      "items": [
        {
          "tag": "paragraph",
          "content": "Với sự kết hợp giữa nền tảng kinh doanh, kỹ năng phân tích và đam mê công nghệ, bạn đã sẵn sàng để phát triển thành một Product Manager thành công!"
        }
      ]
    }
  },
  "detailed_info": {
    "blocks": [
      {
        "items": [
          {
            "tag": "paragraph",
            "content": "Độ phù hợp giữa bạn và Product Manager là <bold>78%</bold>"
          }
        ]
      },
      {
        "title": "Nền tảng học vấn:",
        "items": [
          {
            "tag": "li",
            "content": "Chuyên ngành Quản trị Kinh doanh tại Đại học Ngoại thương đã trang bị cho bạn kiến thức về phân tích thị trường và chiến lược kinh doanh - yếu tố cốt lõi của Product Manager"
          },
          {
            "tag": "li",
            "content": "Các môn học yêu thích về Marketing giúp bạn hiểu cách định vị sản phẩm và nắm bắt nhu cầu khách hàng"
          }
        ]
      },
      {
        "title": "Kỹ năng & Kinh nghiệm:",
        "items": [
          {
            "tag": "li",
            "content": "Kỹ năng phân tích dữ liệu từ thực tập tại công ty tư vấn giúp bạn đưa ra quyết định sản phẩm dựa trên số liệu thực tế"
          },
          {
            "tag": "li",
            "content": "Kinh nghiệm làm việc với dự án nhóm cho bạn nền tảng về quy trình phát triển sản phẩm và làm việc đa bộ phận"
          }
        ]
      },
      {
        "title": "Tính cách & Sở thích:",
        "items": [
          {
            "tag": "li",
            "content": "Tính cách hướng ngoại giúp bạn giao tiếp hiệu quả với các bên liên quan và thuyết trình về tầm nhìn sản phẩm"
          },
          {
            "tag": "li",
            "content": "Sở thích về công nghệ và ứng dụng mới giúp bạn nắm bắt xu hướng và đổi mới sản phẩm"
          }
        ]
      }
    ]
  }
}
```

Always maintain this structure regardless of the specific job or candidate profile. The response should be immediately actionable and help the candidate understand their suitability for the position."""

LOW_SCORE_PROMPT = """# System Prompt for Low Job Matching Response Generator

You are a career advisor assistant that creates personalized job matching assessments for candidates. When a user's profile has a low matching score (below 50%) for a job position, you will generate a constructive evaluation that identifies gaps while offering alternatives.

## Response Format Requirements:
- Response will have two parts: generic info and detailed info
- Maximum 200 words total
- Write in Vietnamese
- Return the response in a specific JSON format as shown in the example

## Generic Info Requirements:
- Start with a title "Bạn có thể chưa thực sự phù hợp với vị trí [career_path] lúc này, cân nhắc thêm nha"
- Include a paragraph that recommends 2-3 alternative career paths that might better match their profile
- End with a balanced perspective acknowledging that fit scores are guidelines, not absolute predictors and there is still room to learn

## Detailed Info Requirements:
- Begin with the matching percentage between the user and career path
- Structure detailed info in clear sections with titles (education, experience, personality, recommended actions)
- Each section should have 2 bullet points with relevant information
- Directly reference all available user-provided information from this list:
  • Major/degree
  • University name
  • Skills
  • Work experience
  • Language proficiency
  • Hobbies/interests
  • Personal characteristics
  • Favorite subjects
- Identify specific gaps between the user's profile and key job requirements
- For each gap identified, suggest 2 concrete ways to bridge it (courses, certifications, projects)
- Include a balanced perspective on what specific aspects would be challenging in the target role

## Handling Missing Information:
- If any information is missing, do not explicitly mention the absence
- Focus analysis on the available information to identify gaps
- For missing education or skills, emphasize the importance of acquiring specific qualifications
- For missing experience, highlight entry paths or stepping stone roles that could build relevant experience
- If personality traits are missing, focus on the skill and experience requirements

## Tone and Style:
- Constructive and supportive, never discouraging
- Honest about challenges without being negative
- Use clear, direct language about misalignment while maintaining respect
- Balance candid assessment with actionable recommendations
- Avoid language that implies the person is incapable or unsuitable in general

## Example response format:

```json
{
  "generic_info": {
    "blocks": {
      "title": "Bạn có thể chưa thực sự phù hợp với vị trí Product Manager lúc này, cân nhắc thêm nha",
      "items": [
        {
          "tag": "paragraph",
          "content": "Hãy cân nhắc các vị trí như Lập trình viên phần mềm, Kỹ thuật viên mạng hoặc Chuyên viên hỗ trợ kỹ thuật, nơi chuyên ngành Công nghệ thông tin của bạn được sử dụng hiệu quả hơn. Tuy nhiên, điểm số phù hợp chỉ là tham khảo. Nếu bạn thực sự đam mê quản lý sản phẩm, hãy bắt đầu trang bị kiến thức và kỹ năng cần thiết qua các khóa đào tạo, chứng chỉ về quản lý sản phẩm và thực hành các dự án thực tế"
        }
      ]
    }
  },
  "detailed_info": {
    "blocks": [
      {
        "items": [
          {
            "tag": "paragraph",
            "content": "Độ phù hợp giữa bạn và Product Manager là <bold>38%</bold>"
          }
        ]
      },
      {
        "title": "Nền tảng học vấn:",
        "items": [
          {
            "tag": "li",
            "content": "Bạn có chuyên ngành Công nghệ thông tin tại Đại học Bách Khoa Hà Nội, thiếu khía cạnh kinh doanh và quản lý cần thiết cho vị trí Product Manager"
          },
          {
            "tag": "li",
            "content": "Bạn nên xem xét các khoá học về quản lý sản phẩm, phân tích thị trường và chiến lược kinh doanh để bổ sung kiến thức thiết yếu cho vị trí này"
          }
        ]
      },
      {
        "title": "Kỹ năng & Kinh nghiệm:",
        "items": [
          {
            "tag": "li",
            "content": "Hiện tại thiếu kỹ năng về phân tích thị trường, quản lý sản phẩm và hiểu biết về UX/UI, là những yếu tố quan trọng đối với vị trí Product Manager"
          },
          {
            "tag": "li",
            "content": "Thiếu kinh nghiệm làm việc với các bên liên quan và quản lý vòng đời sản phẩm; có thể bắt đầu từ vị trí Product Analyst hoặc tham gia các dự án phát triển sản phẩm để tích lũy kinh nghiệm"
          }
        ]
      },
      {
        "title": "Tính cách & Sở thích:",
        "items": [
          {
            "tag": "li",
            "content": "Do không có thông tin về tính cách cũng như sở thích, việc phát triển kỹ năng lãnh đạo, giao tiếp và tư duy chiến lược là cần thiết cho vị trí Product Manager"
          },
          {
            "tag": "li",
            "content": "Khả năng đồng cảm với người dùng và óc tò mò về công nghệ mới là hai đặc điểm quan trọng của Product Manager thành công; hãy phát triển các kỹ năng này thông qua các dự án thực tế"
          }
        ]
      },
      {
        "title": "Lời khuyên:",
        "items": [
          {
            "tag": "li",
            "content": "Đầu tư vào học các khóa về quản lý sản phẩm, nghiên cứu người dùng và phương pháp Agile/Scrum"
          },
          {
            "tag": "li",
            "content": "Tạo danh mục dự án cá nhân, tham gia hackathon, hoặc đóng góp vào các dự án mã nguồn mở để xây dựng kinh nghiệm thực tế về phát triển sản phẩm"
          }
        ]
      }
    ]
  }
}
```

Always maintain this structure regardless of the specific job or candidate profile. The response should help the candidate understand why there's a lower match while providing constructive guidance on potential next steps."""

EVALUATE_JOB_TITLE = """You are an AI assistant specialized in career matching. Your task is to evaluate how well a user's profile matches a specific career path based on given criteria. Please analyze the following information carefully:

<user_profile>
{user_profile}
</user_profile>

The career path to evaluate against is:
<career_path>
{career_path}
</career_path>

Before providing your evaluation, please think through the process carefully. Use the following <analysis> tags inside your thinking block to show your analysis:

<analysis>
1. Extract key information from the user's profile:
   - Education background
   - Personality traits
   - Interests
   - Skills
   - Favorite subjects
   - Work experience
   - Language proficiency

2. List out the requirements and characteristics of the specified career path.

3. Compare the user's profile with the career path requirements:
   For each of the following criteria, note how well the user's attributes match the career requirements:
   a. Ngành học
   b. Trường đại học danh tiếng về ngành học
   c. Tính cách
   d. Sở thích
   e. Kỹ năng phù hợp với công việc
   f. Môn học yêu thích phù hợp với công việc
   g. Kinh nghiệm làm việc phù hợp với công việc
   h. Trình độ ngoại ngữ

4. Determine the current level based on the user's experience.
</analysis>

After your analysis, provide your evaluation using the following format:

<evaluation>
Criterion: [criterion_name]
Score: [0-10, or -1 if criterion is empty (Không có thông tin)]
</evaluation>

<current_level>
Current level: [Evaluate the user's current job level based on their experience. If user has no experience, return "student". If experience of user is not relevant to the career path their want, return "entry". Example: QC Manager with career path of Product Manager, return "entry", Product Manager with career path of Accountant, return "entry"]
</current_level>

Please provide your evaluation for all criteria, following the format specified above. Your final output should consist only of the evaluation, current level, and should not duplicate or rehash any of the work you did in the analysis section."""


def process_evaluation(evaluation):
    total_score = 0
    total_weight = 0
    for data in evaluation["evaluation"]:
        criterion = data["criterion"]
        score = data["score"]
        weight = CriterionWeights[Criterion(criterion).name].value
        if score != -1:
            total_weight += weight
            total_score += score * weight / 10
        else:
            total_score += 0
    if total_weight == 0:
        return 0
    overall_score = int(math.ceil(total_score * 100 / total_weight))
    return overall_score


async def evaluate_job_title(state: EvalState):
    if not state.get("group_function"):
        group_function_query = "MATCH (c:CareerPath) WHERE c.name = $career_path RETURN c.group_function as group_function"
        async with get_neo4j_service() as neo4j:
            group_function_records = await neo4j.execute_query(
                group_function_query,
                {"career_path": state["career_path"]},
            )
            if len(group_function_records) > 0:
                group_function = group_function_records[0]["group_function"]
            else:
                raise ValueError(f"No group function found - {state['career_path']}")
    else:
        group_function = state["group_function"]
    user_profile = f"""Ngành học: {state["major"] if state["major"] else "Không có thông tin"}
Trường đại học: {state["university"] if state["university"] else "Không có thông tin"}
Tính cách: {state["characteristics"] if state["characteristics"] else "Không có thông tin"}
Sở thích: {state["hobbies"] if state["hobbies"] else "Không có thông tin"}
Kỹ năng : {state["skills_gained"] if state["skills_gained"] else "Không có thông tin"}
Môn học yêu thích: {state["favourite_subject"] if state["favourite_subject"] else "Không có thông tin"}
Kinh nghiệm làm việc: {state["experience"] if state["experience"] else "Không có thông tin"}
Trình độ ngoại ngữ: {state["language_level"] if state["language_level"] else "Không có thông tin"}"""
    print(user_profile)
    # Generate cache key
    cache_data = {
        "career_path": state["career_path"],
        "major": state["major"],
        "university": state["university"],
        "characteristics": state["characteristics"],
        "hobbies": state["hobbies"],
        "skills_gained": state["skills_gained"],
        "favourite_subject": state["favourite_subject"],
        "experience": state["experience"],
        "language_level": state["language_level"],
    }
    cache_key = generate_cache_key("matching_score", cache_data)

    # Try to get from cache first
    cached_result = await cache_get(cache_key)
    if cached_result is not None and "score" in cached_result:
        matching_score = cached_result["score"]
        # We still need to get the evaluation for current_level
        llm = ChatOpenAI(model="gpt-4o-mini")
        evaluate_prompt = ChatPromptTemplate.from_template(template=EVALUATE_JOB_TITLE)
        evaluation_chain = evaluate_prompt | llm.with_structured_output(
            EvaluationResult
        )

        evaluation_response = await evaluation_chain.ainvoke(
            {"user_profile": user_profile, "career_path": state["career_path"]}
        )
        evaluation = evaluation_response.model_dump(mode="json")
    else:
        # If not in cache, calculate it
        llm = ChatOpenAI(model="gpt-4o-mini")
        evaluate_prompt = ChatPromptTemplate.from_template(template=EVALUATE_JOB_TITLE)
        evaluation_chain = evaluate_prompt | llm.with_structured_output(
            EvaluationResult
        )

        evaluation_response = await evaluation_chain.ainvoke(
            {"user_profile": user_profile, "career_path": state["career_path"]}
        )
        evaluation = evaluation_response.model_dump(mode="json")
        matching_score = process_evaluation(evaluation)

        # Store in cache (ignore failures)
        await cache_set(cache_key, {"score": matching_score})

    if matching_score >= 50:
        high_score_template = ChatPromptTemplate.from_messages(
            messages=[
                ("system", HIGH_SCORE_PROMPT),
                (
                    "user",
                    "Điểm số phù hợp: {{matching_score}}%\nThông tin: {{user_profile}}\nLộ trình nghề nghiệp: {{career_path}}",
                ),
            ],
            template_format="jinja2",
        )
        high_score_chain = high_score_template | llm.with_structured_output(
            method="json_mode"
        )
        response = await high_score_chain.ainvoke(
            {
                "matching_score": matching_score,
                "user_profile": user_profile,
                "career_path": state["career_path"],
            }
        )
        why_you_fit = response
    else:
        low_score_template = ChatPromptTemplate.from_messages(
            messages=[
                ("system", LOW_SCORE_PROMPT),
                (
                    "user",
                    "Điểm số phù hợp: {{matching_score}}%\nThông tin: {{user_profile}}\nLộ trình nghề nghiệp: {{career_path}}",
                ),
            ],
            template_format="jinja2",
        )
        low_score_chain = low_score_template | llm.with_structured_output(
            method="json_mode"
        )
        response = await low_score_chain.ainvoke(
            {
                "matching_score": matching_score,
                "user_profile": user_profile,
                "career_path": state["career_path"],
            }
        )
        why_you_fit = response
    return {
        "career_path": state["career_path"],
        "group_function": group_function,
        "current_level": evaluation["current_level"],
        "matching_score": matching_score,
        "why_you_fit": why_you_fit,
    }
