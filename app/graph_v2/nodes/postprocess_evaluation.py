from app.graph_v2.state import EvalState
from app.services.neo4j import get_neo4j_service


async def postprocess_evaluation(state: EvalState):
    async with get_neo4j_service() as neo4j:
        path_query = """MATCH (j:JobTitleV2)-[r:BELONGS_TO_PATH]->(c:CareerPath)
WHERE c.name = $career_path AND c.group_function IN [$group_function, "Information Technology"]
RETURN
  COLLECT(CASE WHEN r.level = "entry" AND r.path_type = "manager"
    THEN {job_level: r.level, job_title: j.name_en, salary_min: j.salary_min, salary_max: j.salary_max, market_outlook: j.market_outlook}
    END) AS entry_level_jobs,
  COLLECT(CASE WHEN r.level = "experienced" AND r.path_type = "manager"
    THEN {job_level: r.level, job_title: j.name_en, salary_min: j.salary_min, salary_max: j.salary_max, market_outlook: j.market_outlook}
    END) AS experienced_level_jobs,
  COLLECT(CASE WHEN r.level = "senior" AND r.path_type = "manager"
    THEN {job_level: r.level, job_title: j.name_en, salary_min: j.salary_min, salary_max: j.salary_max, market_outlook: j.market_outlook}
    END) AS senior_level_jobs,
  COLLECT(CASE WHEN r.level = "manager" AND r.path_type = "manager"
    THEN {job_level: r.level, job_title: j.name_en, salary_min: j.salary_min, salary_max: j.salary_max, market_outlook: j.market_outlook}
    END) AS manager_level_jobs,
  COLLECT(CASE WHEN r.level = "director_or_above" AND r.path_type = "manager"
    THEN {job_level: r.level, job_title: j.name_en, salary_min: j.salary_min, salary_max: j.salary_max, market_outlook: j.market_outlook}
    END) AS director_manager_jobs,
  COLLECT(CASE WHEN r.level = "expert" AND r.path_type = "expert"
    THEN {job_level: r.level, job_title: j.name_en, salary_min: j.salary_min, salary_max: j.salary_max, market_outlook: j.market_outlook}
    END) AS expert_level_jobs,
  COLLECT(CASE WHEN r.level = "director_or_above" AND r.path_type = "expert"
    THEN {job_level: r.level, job_title: j.name_en, salary_min: j.salary_min, salary_max: j.salary_max, market_outlook: j.market_outlook}
    END) AS director_expert_jobs"""

        path_records = await neo4j.execute_query(
            path_query,
            {
                "career_path": state["career_path"],
                "group_function": state["group_function"],
            },
        )
        if path_records:
            standard_progression = []
            expert_progression = []
            manager_progression = []
            for record in path_records:
                if record["entry_level_jobs"]:
                    standard_progression.extend(record["entry_level_jobs"])
                if record["experienced_level_jobs"]:
                    standard_progression.extend(record["experienced_level_jobs"])
                if record["senior_level_jobs"]:
                    standard_progression.extend(record["senior_level_jobs"])
                if record["manager_level_jobs"]:
                    manager_progression.extend(record["manager_level_jobs"])
                if record["director_manager_jobs"]:
                    manager_progression.extend(record["director_manager_jobs"])
                if record["expert_level_jobs"]:
                    expert_progression.extend(record["expert_level_jobs"])
                if record["director_expert_jobs"]:
                    expert_progression.extend(record["director_expert_jobs"])

            job_progression = {
                "standard": standard_progression,
                "expert": expert_progression,
                "manager": manager_progression,
            }
            # Generate mock salary data based on job progression
            salary_data = get_salary_data(
                job_progression,
                state["current_level"],
                state["major"],
                state["university"],
            )
            response = {
                "matching_score": state["matching_score"],
                "why_you_fit": state["why_you_fit"],
                "salary": salary_data,
                "statistics": {
                    "percentage": 20.0
                    if state["matching_score"] < 50
                    else 70.0,  # TODO: get from statistics
                    "major": state["major"],
                    "career_path": state["career_path"],
                },
            }
        else:
            raise ValueError("No job progression found")
    return response


def get_salary_data(job_progression, current_level, major, university):
    salary_unit = "triệu VND/tháng"
    standard_progression = job_progression["standard"]
    expert_progression = job_progression["expert"]
    manager_progression = job_progression["manager"]
    # Map job_level to time to achieve
    time_to_achieve_map = {
        "student": "0 years",
        "entry": "0-1 years",
        "experienced": "1-3 years",
        "senior": "3-5 years",
        "expert": "5-7 years",
        "manager": "5-7 years",
        "director_or_above": "7-9 years",
    }

    # Find current level in job progression
    current_level_job = None
    current_level_index = -1

    if current_level == "student":
        salary_data = {
            "current_level": {
                "job_title": "",
                "level": "Student",
                "education": {
                    "major": major,
                    "university": university,
                },
                "salary_range": {
                    "salary_min": 0,
                    "salary_max": 0,
                    "salary_unit": "triệu VND/tháng",
                },
            },
            "standard_level": [],
            "expert_level": [],
            "manager_level": [],
        }
    else:
        for i, level in enumerate(standard_progression):
            if level["job_level"] == current_level:
                current_level_job = level
                current_level_index = i
                break

        if not current_level_job:
            # Default to first level if current level not found
            current_level_job = standard_progression[0]
            current_level_index = 0

        # Generate salary structure
        salary_data = {
            "current_level": {
                "job_title": current_level_job["job_title"],
                "level": current_level_job["job_level"].replace("_", " ").capitalize(),
                "salary_range": {
                    "salary_min": int(current_level_job["salary_min"]),
                    "salary_max": int(current_level_job["salary_max"]),
                    "salary_unit": salary_unit,
                },
            },
            "standard_level": [],
            "expert_level": [],
            "manager_level": [],
        }

    # Generate other levels
    for i, level in enumerate(standard_progression):
        if i > current_level_index:
            level_data = {
                "job_title": level["job_title"],
                "level": level["job_level"].replace("_", " ").capitalize(),
                "salary_range": {
                    "salary_min": int(level["salary_min"]),
                    "salary_max": int(level["salary_max"]),
                    "salary_unit": salary_unit,
                },
                "time_to_achieve": time_to_achieve_map.get(
                    level["job_level"], "1-2 years"
                ),
            }
            salary_data["standard_level"].append(level_data)

    for i, level in enumerate(expert_progression):
        level_data = {
            "job_title": level["job_title"],
            "level": level["job_level"].replace("_", " ").capitalize(),
            "salary_range": {
                "salary_min": int(level["salary_min"]),
                "salary_max": int(level["salary_max"]),
                "salary_unit": salary_unit,
            },
            "time_to_achieve": time_to_achieve_map.get(level["job_level"], "1-2 years"),
        }
        salary_data["expert_level"].append(level_data)

    for i, level in enumerate(manager_progression):
        level_data = {
            "job_title": level["job_title"],
            "level": level["job_level"].replace("_", " ").capitalize(),
            "salary_range": {
                "salary_min": int(level["salary_min"]),
                "salary_max": int(level["salary_max"]),
                "salary_unit": salary_unit,
            },
            "time_to_achieve": time_to_achieve_map.get(level["job_level"], "1-2 years"),
        }
        salary_data["manager_level"].append(level_data)

    return salary_data
