import re

from app.graph_v2.state import AgentState
from app.services.neo4j import get_neo4j_service


def text_to_json(text):
    # Xử lý văn bản đầu vào
    lines = text.strip().split("\n")
    blocks = {"blocks": {"items": []}}

    current_group = None
    i = 0

    while i < len(lines):
        line = lines[i].strip()

        # Bỏ qua dòng trống
        if not line:
            i += 1
            continue

        # Xử lý tiêu đề hoặc đoạn văn thông thường
        if not line.startswith("-") and not line.startswith("•"):
            # Nếu đang có một nhóm mục, thêm nhóm đó vào kết quả
            if current_group:
                blocks["blocks"]["items"].append({"group": current_group})
                current_group = None

            # Thêm đoạn văn
            blocks["blocks"]["items"].append({"tag": "paragraph", "content": line})

        # Xử lý danh sách
        elif line.startswith("-") or line.startswith("•"):
            # Bắt đầu một nhóm mới nếu chưa có
            if current_group is None:
                current_group = []

            # Loại bỏ dấu - hoặc • ở đầu dòng và cắt khoảng trắng
            item_content = re.sub(r"^[-•]\s*", "", line)

            # Thêm mục vào nhóm
            current_group.append({"tag": "li", "content": item_content})

        i += 1

    # Thêm nhóm cuối cùng nếu có
    if current_group:
        blocks["blocks"]["items"].append({"group": current_group})

    return blocks


async def get_career_path(state: AgentState):
    group_function = state.get("group_function", "")
    if state.get("career_paths"):
        career_paths = state["career_paths"]
    else:
        career_paths = [state["career_path"]]

    list_career_path = []
    async with get_neo4j_service() as neo4j:
        for career_path in career_paths:
            if group_function == "":
                group_function_query = "MATCH (c:CareerPath) WHERE c.name = $career_path RETURN c.group_function as group_function"
                group_function_records = await neo4j.execute_query(
                    group_function_query,
                    {"career_path": career_path},
                )
                if len(group_function_records) > 0:
                    group_function = group_function_records[0]["group_function"]
                else:
                    raise ValueError(f"No group function found - {career_path}")
            data = {}
            # Base query for career path info
            career_path_query = """MATCH (j:JobTitleV2)-[r:BELONGS_TO_PATH]->(c:CareerPath) WHERE c.name = $career_path AND r.level = "experienced"
AND c.group_function IN [$group_function, "Information Technology"]
RETURN c.id as career_path_id, c.description as description, c.function as job_function, j.market_outlook as market_outlook, j.salary_min as salary_min, j.salary_max as salary_max"""

            career_path_records = await neo4j.execute_query(
                career_path_query,
                {
                    "career_path": career_path,
                    "group_function": group_function,
                },
            )
            if len(career_path_records) > 0:
                data["career_path"] = career_path
                data["group_function"] = group_function
                career_path_id = career_path_records[0]["career_path_id"]
                data["career_path_id"] = career_path_id
                description = career_path_records[0]["description"]
                data["description"] = text_to_json(description)
                job_function = career_path_records[0]["job_function"]
                data["job_function"] = job_function
                market_outlook = career_path_records[0]["market_outlook"]
                data["market_outlook"] = market_outlook
                salary_min = career_path_records[0]["salary_min"]
                salary_max = career_path_records[0]["salary_max"]
                salary_unit = "triệu VND/tháng"
                data["salary_range"] = {
                    "salary_min": salary_min,
                    "salary_max": salary_max,
                    "salary_unit": salary_unit,
                }
            else:
                raise ValueError(f"No career path found - {career_path}")

            # Base query for skills
            skills_query = """MATCH (j:JobTitleV2)-[r:BELONGS_TO_PATH]->(c:CareerPath)
WHERE c.name = $career_path AND r.level = "experienced"
WITH j
MATCH (j)-[r:UTILIZES]->(s:SkillV2)
WHERE s.type = "soft"
WITH j, COLLECT({name: s.name_en, description: s.description}) AS soft_skills
MATCH (j)-[r:UTILIZES]->(s:SkillV2)
WHERE s.type = "hard"
WITH soft_skills, COLLECT({name: s.name_en, description: s.description}) AS hard_skills
RETURN soft_skills, hard_skills"""

            skills_records = await neo4j.execute_query(
                skills_query,
                {
                    "career_path": career_path,
                    "group_function": group_function,
                },
            )
            if len(skills_records) > 0:
                hard_skills = skills_records[0]["hard_skills"][:4]
                soft_skills = skills_records[0]["soft_skills"][:4]
                data["skills_needed"] = {
                    "technical_skills": hard_skills,
                    "soft_skills": soft_skills,
                }
            else:
                raise ValueError(f"No skills found - {career_path}")
            list_career_path.append(data)

        return {"career_paths": list_career_path}
