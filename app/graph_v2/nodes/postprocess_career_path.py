import asyncio

from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI

from app.graph_v2.nodes.evaluate_job_title import (
    EVALUATE_JOB_TITLE,
    EvaluationResult,
    process_evaluation,
)
from app.graph_v2.state import AgentState
from app.services.cache import cache_get, cache_set, generate_cache_key
from app.services.neo4j import get_neo4j_service

TOP_10_INDUSTRY_TREND = [
    "Applied R&D",
    "Cloud & DevOps",
    "Artificial Intelligence",
    "Auditing & Compliance",
    "Brand Management",
    "Buying & Merchandising",
    "Compliance & Risk (incl. Employment/Retirement Law)",
    "Customer Experience",
    "Cybersecurity",
    "Data & Analytics (Data Eng/AI)",
    "Digital Marketing",
    "Digital Transformation",
    "Electrical & Electronics Engineering",
    "Healthcare Administration",
    "IT Infrastructure & Networking",
    "Market Research & Analysis",
    "Marketing Communications",
    "Mechanical Engineering (incl. Automotive)",
    "Operations Management",
    "Product Design & Development",
    "Product Management",
    "Production & Manufacturing",
    "QA & Testing",
    "Quality Control",
    "Retail Management",
    "Risk Consulting",
    "Risk Management",
    "Software Development",
    "Supply Chain & Logistics",
    "Testing & Inspection",
    "UI/UX Design",
]


async def process_career_path(
    career_path,
    major,
    university,
    characteristics,
    hobbies,
    skills_gained,
    favourite_subject,
    experience,
    language_level,
):
    career_path_name = career_path["career_path"]
    career_path_id = career_path["career_path_id"]
    job_function = career_path["job_function"]
    description = career_path["description"]
    skills_needed = career_path["skills_needed"]
    market_outlook = career_path["market_outlook"]
    salary_range = career_path["salary_range"]
    async with get_neo4j_service() as neo4j:
        group_function_query = "MATCH (c:CareerPath) WHERE c.name = $career_path RETURN c.group_function as group_function"
        group_function_records = await neo4j.execute_query(
            group_function_query,
            {"career_path": career_path_name},
        )
        if len(group_function_records) > 0:
            group_function = group_function_records[0]["group_function"]
        else:
            raise ValueError(f"No group function found - {career_path_name}")

    user_profile = f"""Ngành học: {major if major else "Không có thông tin"}
Trường đại học: {university if university else "Không có thông tin"}
Tính cách: {characteristics if characteristics else "Không có thông tin"}
Sở thích: {hobbies if hobbies else "Không có thông tin"}
Kỹ năng : {skills_gained if skills_gained else "Không có thông tin"}
Môn học yêu thích: {favourite_subject if favourite_subject else "Không có thông tin"}
Kinh nghiệm làm việc: {experience if experience else "Không có thông tin"}
Trình độ ngoại ngữ: {language_level if language_level else "Không có thông tin"}"""

    # Generate cache key
    cache_data = {
        "career_path": career_path_name,
        "major": major,
        "university": university,
        "characteristics": characteristics,
        "hobbies": hobbies,
        "skills_gained": skills_gained,
        "favourite_subject": favourite_subject,
        "experience": experience,
        "language_level": language_level,
    }
    cache_key = generate_cache_key("matching_score", cache_data)

    # Try to get from cache first
    cached_result = await cache_get(cache_key)
    if cached_result is not None and "score" in cached_result:
        matching_score = cached_result["score"]
    else:
        # If not in cache, calculate it
        llm = ChatOpenAI(model="gpt-4o-mini")
        evaluate_prompt = ChatPromptTemplate.from_template(template=EVALUATE_JOB_TITLE)
        evaluation_chain = evaluate_prompt | llm.with_structured_output(
            EvaluationResult
        )

        evaluation_response = await evaluation_chain.ainvoke(
            {"user_profile": user_profile, "career_path": career_path_name}
        )
        evaluation = evaluation_response.model_dump(mode="json")
        matching_score = process_evaluation(evaluation)

        # Store in cache (ignore failures)
        await cache_set(cache_key, {"score": matching_score})

    response = {
        "career_path_id": career_path_id,
        "career_path": career_path_name,
        "group_function": group_function,
        "job_function": job_function,
        "top_industry_trend": 10 if job_function in TOP_10_INDUSTRY_TREND else None,
        "description": description,
        "skills_needed": skills_needed,
        "market_outlook": {"number_jobs": market_outlook},
        "salary_range": salary_range,
        "matching_score": matching_score,
    }
    return response


async def postprocess_career_path(state: AgentState):
    career_paths = state["career_paths"]

    # Process all career paths in parallel
    tasks = [
        process_career_path(
            career_path,
            state["major"],
            state["university"],
            state["characteristics"],
            state["hobbies"],
            state["skills_gained"],
            state["favourite_subject"],
            state["experience"],
            state["language_level"],
        )
        for career_path in career_paths
    ]
    responses = await asyncio.gather(*tasks)
    responses = sorted(responses, key=lambda x: x["matching_score"], reverse=True)
    return {"responses": responses}
