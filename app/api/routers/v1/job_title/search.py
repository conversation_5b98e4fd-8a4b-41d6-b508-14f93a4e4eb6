from fastapi import APIRouter, status
from fastapi.responses import JSONResponse

from app.api.schemas.job_title import JobTitleRequest
from app.handlers.search_job_title import search_job_title

job_title_router = APIRouter(prefix="/internal/v1", tags=["Job Title"])


@job_title_router.post("/search")
async def job_title_search(data: JobTitleRequest):
    try:
        results = await search_job_title(data)

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "code": 200,
                "error": None,
                "data": results,
                "metadata": {},
            },
        )
    except Exception as e:
        return J<PERSON>NResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"code": 500, "error": str(e), "data": None, "metadata": {}},
        )
