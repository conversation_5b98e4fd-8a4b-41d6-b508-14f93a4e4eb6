from dotenv import load_dotenv
from fastapi import APIRouter, status
from fastapi.responses import JSONResponse

from app.graph.graph import graph
from app.models.suggest_career_path import CareerPathInput
from app.services.langfuse import langfuse_handler

load_dotenv()

career_path_test_router = APIRouter(prefix="/internal/v1", tags=["Career Path"])


@career_path_test_router.post("/suggestion-test")
async def career_path_suggestion(data: CareerPathInput):
    data_input = {
        "major": data.major,
        "skills_gained": data.skills_gained,
        "experience": data.experience,
        "university": data.university,
        "language_level": data.language_level,
        "hobbies": data.hobbies,
        "characteristics": data.characteristics,
        "favourite_subject": data.favourite_subject,
    }

    if data.job_title:
        input = {"career_path": data.job_title, **data_input}
    else:
        input = data_input
    try:
        response = await graph.ainvoke(
            input,
            config={"callbacks": [langfuse_handler], "run_name": "Career Path"},
        )

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "code": 200,
                "error": None,
                "data": response["responses"],
                "metadata": {},
            },
        )
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"code": 500, "error": str(e), "data": None, "metadata": {}},
        )
