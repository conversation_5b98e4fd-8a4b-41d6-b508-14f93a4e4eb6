import re
from typing import Any, Dict, List

from app.api.schemas.job_title import JobTitleRequest
from app.services.neo4j import get_neo4j_service


async def search_job_title(data: JobTitleRequest) -> List[Dict[str, Any]]:
    """
    Search for job titles in Neo4j, prioritizing keyword search first and
    falling back to fulltext search for additional results.

    Args:
        data: JobTitleRequest containing search query, limit, and page

    Returns:
        List of job titles with their details, prioritizing exact matches
    """
    query = data.q
    limit = data.limit
    offset = (data.page - 1) * limit

    async with get_neo4j_service() as db:
        # Prepare both queries
        if query:
            # For keyword search, use the original query
            keyword_query = query.lower()

            # Prepare fulltext query for secondary search
            special_chars = r'[+\-&|!(){}[\]^"~*?:\\\/]'
            clean_query = re.sub(special_chars, lambda m: " ", query)
            fulltext_query = f"{clean_query}~"  # Apply fuzzy search after cleaning
        else:
            keyword_query = ""
            fulltext_query = "*"

        # First try with keyword search (CONTAINS)
        keyword_cypher_query = """
        MATCH (j:JobTitleV2)-[r:BELONGS_TO_PATH]->(c:CareerPath)
        WHERE
            c.search_name STARTS WITH $keyword_query AND
            r.level = "experienced" AND
            r.path_type = "manager"
        RETURN
            c.id as career_path_id,
            c.name as career_path,
            c.function as job_function,
            j.salary_min as salary_min,
            j.salary_max as salary_max,
            j.market_outlook as market_outlook,
            j.salary_currency as salary_currency,
            j.salary_unit as salary_unit,
            10.0 as score  // Give keyword results a high score
        """

        # If we have a query and need fulltext search as fallback
        fulltext_cypher_query = """
        CALL db.index.fulltext.queryNodes("career_path_fulltext_index", $fulltext_query)
        YIELD node, score as fulltext_score
        WITH DISTINCT node.name as career_path, fulltext_score
        MATCH (j:JobTitleV2)-[r:BELONGS_TO_PATH]->(c:CareerPath)
        WHERE
            c.name = career_path AND
            r.level = "experienced" AND
            r.path_type = "manager" AND
            fulltext_score > 0
        RETURN
            c.id as career_path_id,
            c.name as career_path,
            c.function as job_function,
            j.salary_min as salary_min,
            j.salary_max as salary_max,
            j.market_outlook as market_outlook,
            j.salary_currency as salary_currency,
            j.salary_unit as salary_unit,
            fulltext_score as score
        """

        # Union query to combine both approaches
        union_query = f"""
        {keyword_cypher_query}
        UNION
        {fulltext_cypher_query}
        ORDER BY score DESC
        SKIP $offset
        LIMIT $limit
        """

        # Always empty query fallback
        empty_query = """
        MATCH (j:JobTitleV2)-[r:BELONGS_TO_PATH]->(c:CareerPath)
        WHERE r.level = "experienced" AND r.path_type = "manager"
        RETURN
            c.id as career_path_id,
            c.name as career_path,
            c.function as job_function,
            j.salary_min as salary_min,
            j.salary_max as salary_max,
            j.market_outlook as market_outlook,
            j.salary_currency as salary_currency,
            j.salary_unit as salary_unit,
            1.0 as score
        ORDER BY c.name
        SKIP $offset
        LIMIT $limit
        """

        # Execute the appropriate query
        if not query:
            # If no query is provided, return all results

            records = await db.execute_query(
                empty_query, {"offset": offset, "limit": limit}
            )
        else:
            # Use the union query to get both keyword and fulltext results
            records = await db.execute_query(
                union_query,
                {
                    "keyword_query": keyword_query,
                    "fulltext_query": fulltext_query,
                    "offset": offset,
                    "limit": limit,
                },
            )

        # Format the results
        results = []
        seen_career_path_ids = set()

        for record in records:
            career_path_id = record["career_path_id"]

            # Skip if we've already seen this career path
            if career_path_id in seen_career_path_ids:
                continue

            seen_career_path_ids.add(career_path_id)

            results.append(
                {
                    "career_path_id": career_path_id,
                    "career_path": record["career_path"],
                    "job_function": record["job_function"],
                    "salary_range": {
                        "salary_min": record["salary_min"],
                        "salary_max": record["salary_max"],
                        "salary_currency": record["salary_currency"],
                        "salary_unit": record["salary_unit"],
                    },
                    "market_outlook": record["market_outlook"],
                    "score": record["score"],
                }
            )

        return results
