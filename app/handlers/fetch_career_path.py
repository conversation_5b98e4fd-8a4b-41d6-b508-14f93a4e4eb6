from typing import List

from app.services.neo4j import get_neo4j_service
from app.services.cache import cache_get, cache_set, generate_cache_key


async def process_get_career_path_by_ids(ids: List[str]):
    # Generate cache key
    cache_key = generate_cache_key("career_paths", {"ids": sorted(ids)})

    # Check cache first
    cached_result = await cache_get(cache_key)
    if cached_result:
        return cached_result

    # If not in cache, fetch from database
    async with get_neo4j_service() as neo4j:
        fetch_career_path_query = """MATCH (c:CareerPath) WHERE c.id IN $ids RETURN c.id as id, c.name as name, c.name_en as name_en;"""
        records = await neo4j.execute_query(fetch_career_path_query, {"ids": ids})
        results = []
        for record in records:
            results.append(
                {
                    "id": record["id"],
                    "name": record["name"],
                    "nameEn": record["name"],
                }
            )

        # Cache the results with 1 hour TTL
        await cache_set(cache_key, results, expire=3600)

        return results
