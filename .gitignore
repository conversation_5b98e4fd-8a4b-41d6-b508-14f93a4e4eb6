# Custom
*.ipynb

# Python-specific
__pycache__/
*.pyc
*.pyd
*.egg
.venv/  # Or venv/, virtualenv/, etc. - adjust to your virtual environment name
.env  # Environment variables file - NEVER commit this!
*.swp  # Swap files (Vim, etc.)
.DS_Store  # macOS Desktop Services Store

# Distribution/packaging
dist/
build/
*.tar.gz
*.zip

# Testing
.pytest_cache/
.coverage
htmlcov/

# IDE-specific
.idea/  # PyCharm
.vscode/ # VS Code
*.iml  # IntelliJ IDEA project files

# Logs and temporary files
*.log
*.tmp
tmp/
temp/

# Data files (if large or frequently changing)
data/  # Or specific data files like large_file.csv
*.csv.gz # Example: compressed data files

# OS-specific
.Thumbs.db  # Windows thumbnail cache
ehthumbs.db  # Windows thumbnail cache

# General
.gitignore  # This file itself (sometimes useful to include, sometimes not)
.gitattributes # Git attributes file

tests/results
