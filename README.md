# Upzi Career Path

Upzi Career Path is an AI-powered career guidance system designed to help Gen Z build personalized career paths. It provides tailored guidance, skill recommendations, and growth opportunities to navigate the evolving job market with confidence.

## Features

- **AI-Powered Career Analysis**: Uses GPT-4 to analyze user profiles and suggest suitable career paths
- **Graph-Based Career Modeling**: Leverages Neo4j to model complex career relationships and progressions
- **Personalized Recommendations**: Analyzes education, skills, interests, and experience for tailored suggestions
- **High Performance**: Redis caching and async operations for fast response times
- **Multi-version API Support**: Gradual migration support with v1 and v2 endpoints
- **Comprehensive Monitoring**: Error tracking with Sentry and LLM observability with Langfuse

## Tech Stack

- **Framework**: FastAPI (Python 3.12+)
- **AI/ML**: LangChain, LangGraph, OpenAI GPT-4
- **Database**: Neo4j (Graph Database)
- **Caching**: Redis
- **Monitoring**: Sentry, Langfuse
- **Package Manager**: UV

## Prerequisites

- Python 3.12 or higher (but less than 3.14)
- Neo4j Database instance
- Redis server
- OpenAI API key
- UV package manager

## Installation

1. **Clone the repository**
   ```bash
   git clone https://git.navigosgroup.com/ai/upzi-career-path.git
   cd upzi-career-path
   ```

2. **Install UV package manager**
   ```bash
   # On macOS/Linux
   curl -LsSf https://astral.sh/uv/install.sh | sh

   # On Windows
   powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
   ```

3. **Create and activate virtual environment**
   ```bash
   uv venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

4. **Install dependencies**
   ```bash
   uv sync
   ```

5. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

6. **Run the application**
   ```bash
   python main.py
   ```

## Project Structure

```
upzi-career-path/
├── app/
│   ├── api/                    # API layer
│   │   ├── routers/           # API endpoints
│   │   │   ├── v1/           # Version 1 endpoints
│   │   │   │   ├── career_path/
│   │   │   │   └── job_title/
│   │   │   └── v2/           # Version 2 endpoints
│   │   └── schemas/          # Pydantic models
│   ├── core/                  # Core configurations
│   │   ├── constants.py      # Application constants
│   │   └── settings.py       # Environment settings
│   ├── exceptions/            # Custom exceptions
│   ├── graph/                 # LangGraph workflows (v1)
│   │   ├── nodes/            # Workflow nodes
│   │   │   ├── suggest_job_title.py
│   │   │   ├── evaluate_job_title.py
│   │   │   ├── get_career_path.py
│   │   │   └── postprocess_career_path.py
│   │   ├── graph.py          # Workflow orchestration
│   │   └── state.py          # State management
│   ├── graph_v2/              # Enhanced workflows (v2)
│   ├── handlers/              # Business logic handlers
│   ├── helpers/               # Utility functions
│   ├── models/                # Data models
│   └── services/              # External service integrations
│       ├── cache.py          # Redis caching
│       ├── langfuse.py       # LLM monitoring
│       └── neo4j.py          # Graph database
├── tests/                     # Test suite
├── logs/                      # Application logs
├── main.py                    # Application entry point
├── pyproject.toml            # Project dependencies
├── uv.lock                   # Dependency lock file
├── docker-compose.yml        # Docker services setup
├── Dockerfile                # Container configuration
└── README.md                 # Project documentation
```

## API Endpoints

### Version 1
- `POST /internal/v1/suggestion` - Get career path suggestions
- `POST /internal/v1/search` - Search for job titles
- `GET /health` - Health check endpoint

### Version 2
- `POST /internal/v2/career-path` - Enhanced career path suggestions with improved algorithms

## Development

### Running Tests
```bash
python run_tests.py
# or
pytest
```

### Code Quality
```bash
# Format code
ruff format .

# Lint code
ruff check .
```

### Using Docker
```bash
# Build and run with Docker Compose
docker-compose up --build

# Run in development mode
docker-compose up
```

## Environment Variables

Key environment variables to configure:

- `OPENAI_API_KEY` - OpenAI API key for GPT-4
- `NEO4J_URI` - Neo4j database connection URI
- `NEO4J_USERNAME` - Neo4j username
- `NEO4J_PASSWORD` - Neo4j password
- `REDIS_URL` - Redis connection URL
- `SENTRY_DSN` - Sentry error tracking DSN
- `LANGFUSE_PUBLIC_KEY` - Langfuse public key
- `LANGFUSE_SECRET_KEY` - Langfuse secret key

## How It Works

1. **User Input**: Users provide their profile information (education, skills, interests) or a specific job title
2. **AI Analysis**: GPT-4 analyzes the profile against 29 predefined career categories
3. **Graph Query**: System queries Neo4j for relevant career paths and relationships
4. **Recommendation**: Returns top 4 most suitable career recommendations
5. **Caching**: Results are cached in Redis for improved performance

## Contributing

1. Create a feature branch from `develop`
2. Make your changes and add tests
3. Ensure all tests pass and code is properly formatted
4. Create a merge request to `develop`

## License

[Add your license information here]

## Support

For issues and questions, please contact the AI team at Navigos Group.
