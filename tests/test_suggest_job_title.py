import asyncio
import json
import os
import time
from typing import List

import pytest

from app.graph.nodes.suggest_job_title import suggest_job_title


async def process_case(idx, case, results_dir):
    # Get case details
    major = case.get("major", "")

    # Run test
    start_time = time.time()
    result = await suggest_job_title(case)
    end_time = time.time()
    print(f"Case {idx} ({major}) - Time taken: {end_time - start_time} seconds")

    # Validate
    assert "career_paths" in result and "group_function" in result
    career_paths = result["career_paths"]
    assert len(career_paths) == 4

    # Store result with case info
    result_entry = {"case_index": idx, "profile": case, "result": result}

    # Write individual result file
    case_file = f"{results_dir}/case_{idx}_{major.replace(' ', '_')}.json"
    with open(case_file, "w", encoding="utf-8") as f:
        json.dump(result_entry, f, ensure_ascii=False, indent=2)

    return result_entry


@pytest.mark.asyncio
async def test_suggest_job_title(test_cases: List[dict]):
    # Create results directory if it doesn't exist
    results_dir = "tests/results"
    os.makedirs(results_dir, exist_ok=True)

    # Clear previous results file
    output_file = f"{results_dir}/job_title_results.json"
    with open(output_file, "w") as f:
        json.dump([], f)

    # Run all cases in parallel
    tasks = [
        process_case(idx, case, results_dir) for idx, case in enumerate(test_cases)
    ]
    all_results = await asyncio.gather(*tasks)

    # Write all results to one file
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
