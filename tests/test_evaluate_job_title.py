import asyncio
import json
import os
import time
from typing import List

import pytest

from app.graph.nodes.evaluate_job_title import evaluate_job_title


async def process_case(idx, case, results_dir):
    # Get case details
    career_path = case.get("career_path", "")

    # Run test
    start_time = time.time()
    result = await evaluate_job_title(case)
    end_time = time.time()
    print(f"Case {idx} ({career_path}) - Time taken: {end_time - start_time} seconds")

    # Validate
    assert "evaluates" in result
    evaluates = result["evaluates"]
    assert career_path in evaluates
    evaluation_result = evaluates[career_path]
    assert "current_level" in evaluation_result
    assert "matching_score" in evaluation_result
    assert "why_you_fit" in evaluation_result

    # Store result with case info
    result_entry = {"case_index": idx, "profile": case, "result": result}

    # Write individual result file
    case_file = (
        f"{results_dir}/evaluate_case_{idx}_{career_path.replace(' ', '_')}.json"
    )
    with open(case_file, "w", encoding="utf-8") as f:
        json.dump(result_entry, f, ensure_ascii=False, indent=2)

    return result_entry


@pytest.mark.asyncio
async def test_evaluate_job_title(evaluate_test_cases: List[dict]):
    # Create results directory if it doesn't exist
    results_dir = "tests/results"
    os.makedirs(results_dir, exist_ok=True)

    # Clear previous results file
    output_file = f"{results_dir}/evaluate_job_title_results.json"
    with open(output_file, "w") as f:
        json.dump([], f)

    # Run all cases in parallel
    tasks = [
        process_case(idx, case, results_dir)
        for idx, case in enumerate(evaluate_test_cases)
    ]
    all_results = await asyncio.gather(*tasks)

    # Write all results to one file
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
