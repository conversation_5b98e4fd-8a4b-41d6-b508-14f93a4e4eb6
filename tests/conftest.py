import pytest


@pytest.fixture
def test_cases():
    return [
        {
            "major": "Công nghệ thông tin",
            "university": "<PERSON><PERSON><PERSON> học <PERSON> K<PERSON>",
            "characteristics": "",
            "skills_gained": "",
            "experience": "",
            "hobbies": "",
            "favourite_subject": "",
            "language_level": "",
        },
        {
            "major": "Quản trị kinh doanh",
            "university": "Đại học Kinh tế TP. Hồ Chí Minh",
            "characteristics": "<PERSON><PERSON><PERSON> tiếp tốt, tư duy chiến lư<PERSON>, kh<PERSON> năng thích ứng cao",
            "skills_gained": "<PERSON><PERSON> tích thị trường, quản lý nhân sự, lập kế hoạch kinh doanh, marketing số",
            "experience": "Thực tập tại Unilever Việt Nam, tham gia câu lạc bộ khởi nghiệp sinh viên",
            "hobbies": "<PERSON> lị<PERSON>, nhi<PERSON><PERSON>nh, tham gia các hoạt động tình nguyện",
            "favourite_subject": "Marketing chiến lược",
            "language_level": "Tiếng Anh TOEIC 850, tiếng Trung HSK 3",
        },
        {
            "major": "Kỹ thuật điện - điện tử",
            "university": "Đại học Đà Nẵng",
            "characteristics": "Cẩn thận, tỉ mỉ, có tư duy logic và khả năng giải quyết vấn đề",
            "skills_gained": "Thiết kế mạch điện, lập trình vi điều khiển, tự động hóa, IoT",
            "experience": "Thực tập tại nhà máy Samsung Thái Nguyên, tham gia dự án nghiên cứu về năng lượng tái tạo",
            "hobbies": "Chế tạo robot, sửa chữa thiết bị điện tử, đi xe đạp",
            "favourite_subject": "Hệ thống nhúng",
            "language_level": "Tiếng Anh B2, tiếng Hàn sơ cấp",
        },
        {
            "major": "Y khoa",
            "university": "Đại học Y Hà Nội",
            "characteristics": "Tận tâm, trách nhiệm, chịu được áp lực cao, kỹ năng giao tiếp tốt",
            "skills_gained": "Chẩn đoán lâm sàng, phẫu thuật cơ bản, xử lý cấp cứu, nghiên cứu y học",
            "experience": "Thực tập nội trú tại Bệnh viện Bạch Mai, tham gia đội tình nguyện y tế vùng cao",
            "hobbies": "Chơi đàn piano, đọc sách y khoa, tham gia các hội thảo y học",
            "favourite_subject": "Nội khoa và tim mạch",
            "language_level": "Tiếng Anh chuyên ngành y tốt, tiếng Pháp cơ bản",
        },
        {
            "major": "Ngoại ngữ - Du lịch",
            "university": "Đại học Mở TP. Hồ Chí Minh",
            "characteristics": "Hòa đồng, linh hoạt, giao tiếp tốt, tư duy đa văn hóa",
            "skills_gained": "Quản lý tour, hướng dẫn du lịch, giao tiếp đa ngôn ngữ, xử lý tình huống",
            "experience": "Làm hướng dẫn viên bán thời gian cho Saigontourist, thực tập tại khách sạn 5 sao",
            "hobbies": "Khám phá ẩm thực, học ngôn ngữ mới, viết blog du lịch",
            "favourite_subject": "Văn hóa các nước Đông Nam Á",
            "language_level": "Tiếng Anh C1, tiếng Pháp B2, tiếng Hàn B1",
        },
        {
            "major": "Kế toán - Kiểm toán",
            "university": "Học viện Tài chính",
            "characteristics": "Tỉ mỉ, chính xác, có trách nhiệm cao, tư duy phân tích tốt",
            "skills_gained": "Lập báo cáo tài chính, sử dụng phần mềm MISA, phân tích dữ liệu tài chính, thuế",
            "experience": "Thực tập tại công ty kiểm toán KPMG, làm kế toán bán thời gian tại doanh nghiệp vừa và nhỏ",
            "hobbies": "Chơi cờ tướng, đọc sách về tài chính, tham gia câu lạc bộ đầu tư",
            "favourite_subject": "Kế toán quản trị và phân tích tài chính",
            "language_level": "Tiếng Anh TOEIC 750, tiếng Nhật N5",
        },
        {
            "major": "Công nghệ thực phẩm",
            "university": "Đại học Cần Thơ",
            "characteristics": "Cẩn thận, kiên nhẫn, tư duy khoa học, sáng tạo",
            "skills_gained": "Phân tích chất lượng thực phẩm, phát triển sản phẩm mới, quản lý an toàn thực phẩm",
            "experience": "Thực tập tại Vinamilk, tham gia nghiên cứu về chế biến nông sản vùng ĐBSCL",
            "hobbies": "Nấu ăn, làm vườn, thí nghiệm chế biến thực phẩm tại nhà",
            "favourite_subject": "Công nghệ lên men và bảo quản thực phẩm",
            "language_level": "Tiếng Anh B1, tiếng Thái cơ bản",
        },
        {
            "major": "Kiến trúc và Quy hoạch",
            "university": "Đại học Kiến trúc TP. Hồ Chí Minh",
            "characteristics": "Sáng tạo, tỉ mỉ, thẩm mỹ tốt, tư duy không gian",
            "skills_gained": "Thiết kế AutoCAD, Sketchup, Revit, phác thảo tay, quy hoạch đô thị, kiến trúc xanh",
            "experience": "Thực tập tại công ty tư vấn thiết kế VinArch, tham gia cuộc thi kiến trúc sinh viên châu Á",
            "hobbies": "Vẽ tranh, du lịch khám phá kiến trúc, mô hình hóa",
            "favourite_subject": "Kiến trúc bền vững và thiết kế đô thị",
            "language_level": "Tiếng Anh B2, tiếng Ý cơ bản",
        },
        {
            "major": "Công nghệ sinh học",
            "university": "Đại học Quốc gia Hà Nội",
            "characteristics": "Tỉ mỉ, kiên nhẫn, tư duy khoa học, ham học hỏi",
            "skills_gained": "Kỹ thuật sinh học phân tử, nuôi cấy mô, phân tích gen, công nghệ enzyme",
            "experience": "Thực tập tại Viện Công nghệ sinh học, tham gia dự án nghiên cứu về nông nghiệp công nghệ cao",
            "hobbies": "Đọc sách khoa học, tham gia các hội thảo khoa học, làm thí nghiệm",
            "favourite_subject": "Di truyền học và sinh học phân tử",
            "language_level": "Tiếng Anh học thuật tốt, tiếng Đức A2",
        },
        {
            "major": "Truyền thông đa phương tiện",
            "university": "Đại học FPT",
            "characteristics": "Sáng tạo, nhạy bén với xu hướng, khả năng làm việc nhóm tốt",
            "skills_gained": "Thiết kế đồ họa, quay phim, biên tập video, quản lý mạng xã hội, marketing số",
            "experience": "Thực tập tại công ty truyền thông Dentsu, quản lý fanpage cho startup thời trang",
            "hobbies": "Chụp ảnh, làm video ngắn, theo dõi xu hướng công nghệ và mạng xã hội",
            "favourite_subject": "Sản xuất nội dung số và kể chuyện truyền thông",
            "language_level": "Tiếng Anh C1, tiếng Hàn B1",
        },
    ]


@pytest.fixture
def evaluate_test_cases():
    return [
        {
            "major": "Công nghệ thông tin",
            "university": "Đại học Bách Khoa Hà Nội",
            "characteristics": "Tỉ mỉ, logic, kiên nhẫn, thích giải quyết vấn đề",
            "skills_gained": "Lập trình Python, Java, phát triển web, phân tích dữ liệu, machine learning",
            "experience": "",
            "hobbies": "Chơi game, tham gia hackathon, đọc sách kỹ thuật",
            "favourite_subject": "Cấu trúc dữ liệu và giải thuật",
            "language_level": "Tiếng Anh TOEIC 750",
            "career_path": "Software Developer",
        },
        {
            "major": "Truyền thông đa phương tiện",
            "university": "Đại học FPT",
            "characteristics": "Sáng tạo, nhạy bén với xu hướng, khả năng làm việc nhóm tốt",
            "skills_gained": "Thiết kế đồ họa, quay phim, biên tập video, quản lý mạng xã hội, marketing số",
            "experience": "",
            "hobbies": "Chụp ảnh, làm video ngắn, theo dõi xu hướng công nghệ và mạng xã hội",
            "favourite_subject": "Sản xuất nội dung số và kể chuyện truyền thông",
            "language_level": "Tiếng Anh C1, tiếng Hàn B1",
            "career_path": "Content Creator",
        },
    ]
