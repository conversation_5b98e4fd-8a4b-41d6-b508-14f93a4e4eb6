import logging
import os

import sentry_sdk
import uvicorn
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.starlette import StarletteIntegration

from app.api.routers.health import health_router
from app.api.routers.v1.career_path.suggestion import career_path_router
from app.api.routers.v1.career_path.test import career_path_test_router
from app.api.routers.v1.job_title.search import job_title_router
from app.api.routers.v2.career_path.api import career_path_router_v2
from app.core.constants import LOGGING_CONFIG
from app.core.settings import get_settings

settings = get_settings()

os.makedirs("logs", exist_ok=True)
logging.config.dictConfig(LOGGING_CONFIG)

sentry_sdk.init(
    dsn=settings.sentry_dsn,
    traces_sample_rate=0.1,
    integrations=[
        StarletteIntegration(),
        FastApiIntegration(),
    ],
)

app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


app.include_router(health_router)
app.include_router(career_path_router)
app.include_router(job_title_router)
app.include_router(career_path_test_router)
app.include_router(career_path_router_v2)


if __name__ == "__main__":
    reload = True if settings.app_env == "dev" else False

    uvicorn.run(
        app="main:app",
        host=settings.app_host,
        port=settings.app_port,
        log_config=LOGGING_CONFIG,
        reload=reload,
    )
